2025-05-30 15:27:59 | TradingBot | INFO | 🔍 Starting analysis for USD/JPY
2025-05-30 15:27:59 | EnhancedMarketData | INFO | 🔍 Fetching comprehensive data for USD/JPY (Enhanced)
2025-05-30 15:27:59 | MarketData | INFO | 🔍 Fetching comprehensive data for USD/JPY
2025-05-30 15:28:01 | MarketData | INFO | 💰 Quote for USD/JPY: $143.9290
2025-05-30 15:28:02 | MarketData | INFO | 📊 Retrieved 100 intraday data points for USD/JPY
2025-05-30 15:28:03 | MarketData | INFO | 📊 Retrieved 100 daily data points for USD/JPY
2025-05-30 15:28:04 | MarketData | INFO | 📈 Retrieved RSI data for USD/JPY
2025-05-30 15:28:06 | MarketData | INFO | 📈 Retrieved MACD data for USD/JPY
2025-05-30 15:28:07 | MarketData | INFO | 📈 Retrieved EMA data for USD/JPY
2025-05-30 15:28:08 | MarketData | INFO | 📈 Retrieved BBANDS data for USD/JPY
2025-05-30 15:28:08 | TradingBot | INFO | [UP] USD/JPY: $143.93 (+0.00%)
2025-05-30 15:28:08 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/JPY
2025-05-30 15:28:24 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 15:28:24 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/JPY | Confidence: 60.00%
2025-05-30 15:28:24 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a neutral to slightly bearish outlook for USD/JPY. The RSI is at 47.51, indicating neither overbought nor oversold conditions, suggesting a lack of strong directional momentum. The MACD is slightly negative with a histogram value of -0.0168, indicating a weak bearish momentum. The current price is below the EMA of 144.3073, suggesting a bearish trend. However, the price is within the Bollinger Bands, indicating no extreme volatility or breakout. Given these mixed signals and the lack of a strong trend, it is prudent to hold off on entering a new position until clearer signals emerge.
2025-05-30 15:28:24 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 15:29:24 | TradingBot | INFO | 🔍 Starting analysis for USD/JPY
2025-05-30 15:29:24 | EnhancedMarketData | INFO | 🔍 Fetching comprehensive data for USD/JPY (Enhanced)
2025-05-30 15:29:24 | MarketData | INFO | 🔍 Fetching comprehensive data for USD/JPY
2025-05-30 15:29:25 | MarketData | INFO | 💰 Quote for USD/JPY: $143.9610
2025-05-30 15:29:27 | MarketData | INFO | 📊 Retrieved 100 intraday data points for USD/JPY
2025-05-30 15:29:28 | MarketData | INFO | 📊 Retrieved 100 daily data points for USD/JPY
2025-05-30 15:29:29 | MarketData | INFO | 📈 Retrieved RSI data for USD/JPY
2025-05-30 15:29:30 | MarketData | INFO | 📈 Retrieved MACD data for USD/JPY
2025-05-30 15:29:31 | MarketData | INFO | 📈 Retrieved EMA data for USD/JPY
2025-05-30 15:29:33 | MarketData | INFO | 📈 Retrieved BBANDS data for USD/JPY
2025-05-30 15:29:33 | TradingBot | INFO | [UP] USD/JPY: $143.96 (+0.00%)
2025-05-30 15:29:33 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/JPY
2025-05-30 15:29:44 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 15:29:44 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/JPY | Confidence: 60.00%
2025-05-30 15:29:44 | SignalGenerator | INFO | [REASONING] The current technical indicators and price action suggest a cautious approach. The RSI is neutral at 47.6408, indicating neither overbought nor oversold conditions. The MACD is slightly negative, with a histogram value of -0.0148, suggesting weak bearish momentum. The current price is below the EMA of 144.3104, indicating a potential downtrend. However, the recent price action shows a slight upward momentum with the last close at 143.96100, which is the highest close in the recent periods. The Bollinger Bands indicate the price is near the lower band, suggesting potential support. Given these mixed signals and the absence of strong directional momentum, it is prudent to hold off on entering a new position until clearer signals emerge.
2025-05-30 15:29:44 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 15:29:56 | TradingBot | INFO | [SHUTDOWN] Shutdown signal received, stopping bot...
2025-05-30 15:29:56 | EnhancedMarketData | INFO | 🧹 Enhanced market data provider cleaned up
2025-05-30 15:29:56 | TradingBot | INFO | 🛑 Trading bot stopped
2025-05-30 15:29:56 | EnhancedMarketData | INFO | 🧹 Enhanced market data provider cleaned up
2025-05-30 15:29:56 | TradingBot | INFO | 🛑 Trading bot stopped
2025-05-30 15:45:31 | TradingBot | INFO | 🔍 Starting analysis for AAPL
2025-05-30 15:45:31 | EnhancedMarketData | INFO | 🔍 Fetching comprehensive data for AAPL (Enhanced)
2025-05-30 15:45:31 | MarketData | INFO | 🔍 Fetching comprehensive data for AAPL
2025-05-30 15:45:33 | MarketData | INFO | 💰 Quote for AAPL: $200.0600
2025-05-30 15:45:34 | MarketData | INFO | 📊 Retrieved 100 intraday data points for AAPL
2025-05-30 15:45:35 | MarketData | INFO | 📊 Retrieved 100 daily data points for AAPL
2025-05-30 15:45:36 | MarketData | INFO | 📈 Retrieved RSI data for AAPL
2025-05-30 15:45:37 | MarketData | INFO | 📈 Retrieved MACD data for AAPL
2025-05-30 15:45:39 | MarketData | INFO | 📈 Retrieved EMA data for AAPL
2025-05-30 15:45:40 | MarketData | INFO | 📈 Retrieved BBANDS data for AAPL
2025-05-30 15:45:40 | TradingBot | INFO | [UP] AAPL: $200.06 (+0.06%)
2025-05-30 15:45:40 | AIAnalyzer | INFO | [AI] Analyzing market data for AAPL
2025-05-30 15:45:55 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 15:45:55 | SignalGenerator | INFO | [SIGNAL] HOLD | AAPL | Confidence: 60.00%
2025-05-30 15:45:55 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a cautious approach. The RSI is at 44.65, indicating that the stock is neither overbought nor oversold, suggesting a neutral momentum. The MACD is negative with a histogram below zero, indicating bearish momentum, but it is not strongly negative, which suggests a lack of strong selling pressure. The price is currently below the EMA of 203.28, indicating a short-term downtrend. However, the price is near the middle band of the Bollinger Bands, suggesting potential support around this level. The recent price action shows a gradual upward movement with higher highs and higher lows, but the volume is not significantly increasing, which raises concerns about the strength of the move. Given the lack of strong bullish signals and the current market conditions, it is prudent to hold off on entering a new position until clearer signals emerge.
2025-05-30 15:45:55 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 16:19:53 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:19:53 | EnhancedMarketData | INFO | 🔍 Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:19:53 | MarketData | INFO | 🔍 Fetching comprehensive data for USD/CAD
2025-05-30 16:20:04 | MarketData | ERROR | [ERROR] in Twelve Data API request: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /quote?symbol=USD%2FCAD&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D730D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\socket.py", line 954, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 715, in urlopen
    httplib_response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 404, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 1058, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x00000182D7D730D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 799, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /quote?symbol=USD%2FCAD&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D730D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Desktop\FX\market_data.py", line 36, in _make_request
    response = self.session.get(url, params=params, timeout=30)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /quote?symbol=USD%2FCAD&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D730D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-05-30 16:20:16 | MarketData | ERROR | [ERROR] in Twelve Data API request: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1min&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D735E0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\socket.py", line 954, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 715, in urlopen
    httplib_response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 404, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 1058, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x00000182D7D735E0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 799, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1min&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D735E0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Desktop\FX\market_data.py", line 36, in _make_request
    response = self.session.get(url, params=params, timeout=30)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1min&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D735E0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-05-30 16:20:29 | MarketData | ERROR | [ERROR] in Twelve Data API request: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1day&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7E2DD30>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\socket.py", line 954, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 715, in urlopen
    httplib_response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 404, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 1058, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x00000182D7E2DD30>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 799, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1day&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7E2DD30>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Desktop\FX\market_data.py", line 36, in _make_request
    response = self.session.get(url, params=params, timeout=30)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1day&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7E2DD30>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-05-30 16:20:39 | MarketData | INFO | 📈 Retrieved RSI data for USD/CAD
2025-05-30 16:20:40 | MarketData | INFO | 📈 Retrieved MACD data for USD/CAD
2025-05-30 16:20:41 | MarketData | INFO | 📈 Retrieved EMA data for USD/CAD
2025-05-30 16:20:42 | MarketData | INFO | 📈 Retrieved BBANDS data for USD/CAD
2025-05-30 16:20:42 | TradingBot | ERROR | Failed to fetch market data for USD/CAD
2025-05-30 16:21:12 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:21:12 | EnhancedMarketData | INFO | 🔍 Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:21:12 | MarketData | INFO | 🔍 Fetching comprehensive data for USD/CAD
2025-05-30 16:21:13 | MarketData | INFO | 💰 Quote for USD/CAD: $1.3720
2025-05-30 16:21:15 | MarketData | INFO | 📊 Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:21:16 | MarketData | INFO | 📊 Retrieved 100 daily data points for USD/CAD
2025-05-30 16:21:17 | MarketData | INFO | 📈 Retrieved RSI data for USD/CAD
2025-05-30 16:21:18 | MarketData | INFO | 📈 Retrieved MACD data for USD/CAD
2025-05-30 16:21:19 | MarketData | INFO | 📈 Retrieved EMA data for USD/CAD
2025-05-30 16:21:21 | MarketData | INFO | 📈 Retrieved BBANDS data for USD/CAD
2025-05-30 16:21:21 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:21:21 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:21:34 | AIAnalyzer | INFO | [AI] Analysis complete: BUY (confidence: 70.00%)
2025-05-30 16:21:34 | SignalGenerator | INFO | [STATE_CHANGE] USD/CAD: NO_POSITION → LONG_POSITION
2025-05-30 16:21:34 | SignalGenerator | ERROR | Error exporting single signal: [Errno 2] No such file or directory: 'signal_buy_USD/CAD_20250530_162134.json'
2025-05-30 16:21:34 | SignalGenerator | INFO | [SIGNAL] BUY | USD/CAD | Confidence: 70.00%
2025-05-30 16:21:34 | SignalGenerator | INFO | [REASONING] The USD/CAD pair is currently trading near the lower Bollinger Band, suggesting potential support and a possible reversal. The RSI is at 34.95, indicating the pair is approaching oversold conditions, which could lead to a buying opportunity. Despite the MACD being slightly negative, the histogram is close to zero, suggesting that bearish momentum may be weakening. The recent price action shows consolidation around the 1.37200 level, which could act as a support. Given these technical indicators, a short-term buy position could be considered.
2025-05-30 16:21:34 | SignalGenerator | INFO | [APPROVE] Signal approved for execution: BUY USD/CAD (Confidence: 70.00%)
2025-05-30 16:22:03 | TradingBot | INFO | [SHUTDOWN] Shutdown signal received, stopping bot...
2025-05-30 16:22:03 | SignalGenerator | INFO | [EXPORT] Exported 1 signals to trading_signals_20250530_162203.json
2025-05-30 16:22:03 | EnhancedMarketData | INFO | 🧹 Enhanced market data provider cleaned up
2025-05-30 16:22:03 | TradingBot | INFO | Trading bot stopped
2025-05-30 16:22:03 | SignalGenerator | INFO | [EXPORT] Exported 1 signals to trading_signals_20250530_162203.json
2025-05-30 16:22:03 | EnhancedMarketData | INFO | 🧹 Enhanced market data provider cleaned up
2025-05-30 16:22:03 | TradingBot | INFO | Trading bot stopped
2025-05-30 16:31:39 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:31:40 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:31:41 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:31:41 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:31:41 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:31:41 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:31:42 | MarketData | INFO | Quote for USD/CAD: $1.3734
2025-05-30 16:31:43 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:31:44 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:31:44 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:31:45 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:31:45 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:31:46 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:31:46 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:31:48 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:31:48 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:31:49 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:31:49 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:31:51 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:31:51 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:31:51 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:31:56 | AIAnalyzer | INFO | [AI] Analysis complete: BUY (confidence: 70.00%)
2025-05-30 16:31:56 | SignalGenerator | INFO | [STATE_CHANGE] USD/CAD: NO_POSITION → LONG_POSITION
2025-05-30 16:31:56 | SignalGenerator | ERROR | Error exporting single signal: [Errno 2] No such file or directory: 'signal_buy_USD/CAD_20250530_163156.json'
2025-05-30 16:31:56 | SignalGenerator | INFO | [SIGNAL] BUY | USD/CAD | Confidence: 70.00%
2025-05-30 16:31:56 | SignalGenerator | INFO | [REASONING] The USD/CAD pair is showing signs of potential upward momentum. The RSI is at 35.9477, indicating that the pair is approaching oversold conditions, which could lead to a reversal. The MACD is slightly negative, but the histogram shows a narrowing, suggesting a potential bullish crossover. The current price is near the lower Bollinger Band, which often acts as a support level, and the price has been gradually increasing over the last few periods. Additionally, the recent price action shows higher lows and higher highs, indicating a potential uptrend. Given the current technical setup and the absence of significant economic data releases that could disrupt this trend, a cautious buy is recommended.
2025-05-30 16:31:56 | SignalGenerator | INFO | [APPROVE] Signal approved for execution: BUY USD/CAD (Confidence: 70.00%)
2025-05-30 16:32:56 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:32:57 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:32:58 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:32:58 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:32:58 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:32:58 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:32:59 | MarketData | INFO | Quote for USD/CAD: $1.3734
2025-05-30 16:32:59 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:33:00 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:33:00 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:33:02 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:33:02 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:33:03 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:33:03 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:33:04 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:33:04 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:33:06 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:33:06 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:33:07 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:33:07 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:33:07 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:33:14 | AIAnalyzer | INFO | [AI] Analysis complete: EXIT (confidence: 70.00%)
2025-05-30 16:33:14 | SignalGenerator | INFO | [STATE_CHANGE] USD/CAD: LONG_POSITION → NO_POSITION
2025-05-30 16:33:14 | SignalGenerator | ERROR | Error exporting single signal: [Errno 2] No such file or directory: 'signal_exit_USD/CAD_20250530_163314.json'
2025-05-30 16:33:14 | SignalGenerator | INFO | [SIGNAL] EXIT | USD/CAD | Confidence: 70.00%
2025-05-30 16:33:14 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a bearish sentiment. The RSI is at 35.98, indicating the pair is approaching oversold territory but not yet at a reversal point. The MACD is negative with a histogram below zero, suggesting bearish momentum. The current price is below the EMA of 1.3841, indicating a downtrend. The price is also near the lower Bollinger Band, which could suggest limited downside potential but also reflects current bearish pressure. Given the current P&L is at breakeven, it is prudent to exit the position to prevent potential losses if the downtrend continues.

Current Position: LONG_POSITION at 1.37339. Current P&L: +0.00%
2025-05-30 16:33:14 | SignalGenerator | INFO | [APPROVE] EXIT signal approved for execution: EXIT USD/CAD (Confidence: 70.00%)
2025-05-30 16:34:14 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:34:14 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:34:15 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:34:15 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:34:15 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:34:15 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:34:16 | MarketData | INFO | Quote for USD/CAD: $1.3735
2025-05-30 16:34:16 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:34:18 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:34:18 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:34:19 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:34:19 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:34:20 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:34:20 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:34:22 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:34:22 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:34:23 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:34:23 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:34:24 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:34:24 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:34:24 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:34:29 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 16:34:42 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 60.00%
2025-05-30 16:34:42 | SignalGenerator | INFO | [REASONING] The USD/CAD pair is currently showing mixed signals. The RSI is at 35.9551, indicating that the pair is approaching oversold territory, which could suggest a potential buying opportunity. However, the MACD is negative with a MACD histogram of -0.0009, indicating bearish momentum. The current exchange rate is below the EMA of 1.3841, suggesting a downtrend. Additionally, the price is near the lower Bollinger Band at 1.3699, which could act as a support level. Given these mixed signals and the lack of a strong directional trend, it is prudent to hold and wait for clearer signals before entering a trade.
2025-05-30 16:34:42 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 16:35:42 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:35:43 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:35:44 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:35:44 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:35:44 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:35:44 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:35:45 | MarketData | INFO | Quote for USD/CAD: $1.3736
2025-05-30 16:35:45 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:35:46 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:35:46 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:35:48 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:35:48 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:35:49 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:35:49 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:35:50 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:35:50 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:35:52 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:35:52 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:35:53 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:35:53 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:35:53 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:35:58 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 16:35:58 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 60.00%
2025-05-30 16:35:58 | SignalGenerator | INFO | [REASONING] The USD/CAD pair is currently showing mixed signals. The RSI is at 36.1250, indicating that the pair is approaching oversold territory, which could suggest a potential buying opportunity. However, the MACD is negative with a histogram also in the negative, suggesting bearish momentum. The current price is below the EMA of 1.3841, indicating a downtrend. The price is also near the lower Bollinger Band, which could imply a potential bounce. Despite these mixed signals, the lack of a clear trend direction and the current market volatility suggest a cautious approach. Therefore, holding off on entering a new position until clearer signals emerge is advisable.
2025-05-30 16:35:58 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 16:36:58 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:36:58 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:37:00 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:37:00 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:37:00 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:37:00 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:37:01 | MarketData | INFO | Quote for USD/CAD: $1.3737
2025-05-30 16:37:01 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:37:02 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:37:02 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:37:03 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:37:04 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:37:05 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:37:05 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:37:06 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:37:06 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:37:07 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:37:07 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:37:09 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:37:09 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:37:09 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:37:17 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 16:37:17 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 60.00%
2025-05-30 16:37:17 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a cautious approach. The RSI is at 36.1546, indicating the pair is approaching oversold territory but not yet at a critical level. The MACD is negative with a slight bearish histogram, suggesting a weak bearish momentum. The exchange rate is below the EMA of 1.3841, indicating a short-term downtrend. However, the price is near the lower Bollinger Band at 1.3700, which could act as support. Given the lack of strong bullish or bearish signals, it's prudent to hold and wait for clearer market direction.
2025-05-30 16:37:18 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 16:38:18 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:38:18 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:38:19 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:38:19 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:38:19 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:38:19 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:38:20 | MarketData | INFO | Quote for USD/CAD: $1.3736
2025-05-30 16:38:20 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:38:22 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:38:22 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:38:23 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:38:23 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:38:24 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:38:24 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:38:26 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:38:26 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:38:27 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:38:27 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:38:28 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:38:28 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:38:28 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:38:40 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 16:38:40 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 60.00%
2025-05-30 16:38:40 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a cautious approach. The RSI is at 36.0730, indicating the pair is approaching oversold conditions, but not yet at a level that typically signals a strong buy opportunity. The MACD is negative, with a slight bearish divergence, suggesting downward momentum. The EMA is significantly higher than the current price, indicating a bearish trend. Bollinger Bands show the price is near the lower band, suggesting potential support, but not a clear reversal signal. Given the lack of strong bullish indicators and the current market dynamics, it is prudent to hold and wait for clearer signals before entering a position.
2025-05-30 16:38:40 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 16:39:40 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:39:40 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:39:42 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:39:42 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:39:42 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:39:42 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:39:43 | MarketData | INFO | Quote for USD/CAD: $1.3735
2025-05-30 16:39:43 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:39:44 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:39:44 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:39:46 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:39:46 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:39:47 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:39:47 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:39:48 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:39:48 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:39:50 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:39:50 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:39:51 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:39:51 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:39:51 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:40:04 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 16:40:04 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 60.00%
2025-05-30 16:40:04 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a cautious approach. The RSI is at 36.0287, indicating the pair is nearing oversold territory but not yet there, which could imply potential for a bounce. The MACD is negative, with a histogram showing a slight bearish momentum, suggesting the downtrend might continue. The EMA is significantly higher than the current price, indicating a bearish trend. However, the price is close to the lower Bollinger Band, which could act as a support level. Given the lack of a strong directional signal and the current market conditions, it is prudent to hold off on entering a new position until clearer signals emerge.
2025-05-30 16:40:04 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 16:41:04 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:41:05 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:41:06 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:41:06 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:41:06 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:41:06 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:41:07 | MarketData | INFO | Quote for USD/CAD: $1.3738
2025-05-30 16:41:07 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:41:08 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:41:08 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:41:10 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:41:10 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:41:11 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:41:11 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:41:12 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:41:12 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:41:14 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:41:14 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:41:15 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:41:15 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:41:15 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:41:20 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 16:41:20 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 60.00%
2025-05-30 16:41:20 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a cautious approach. The RSI is at 36.2291, indicating that the USD/CAD pair is approaching oversold territory, but not quite there yet. The MACD is negative with a slight bearish divergence, suggesting downward momentum. The price is currently below the EMA of 1.3841, indicating a bearish trend. However, the price is close to the lower Bollinger Band at 1.3700, which may act as a support level. Given these mixed signals and the lack of a clear breakout or reversal pattern, it is prudent to hold off on entering a new position until stronger signals emerge.
2025-05-30 16:41:20 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 16:42:20 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:42:21 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:42:22 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:42:22 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:42:22 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:42:22 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:42:23 | MarketData | INFO | Quote for USD/CAD: $1.3741
2025-05-30 16:42:23 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:42:24 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:42:24 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:42:26 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:42:26 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:42:27 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:42:27 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:42:28 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:42:28 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:42:30 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:42:30 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:42:31 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:42:31 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:42:31 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:42:37 | AIAnalyzer | INFO | [AI] Analysis complete: BUY (confidence: 70.00%)
2025-05-30 16:42:37 | SignalGenerator | INFO | [STATE_CHANGE] USD/CAD: NO_POSITION → LONG_POSITION
2025-05-30 16:42:37 | SignalGenerator | ERROR | Error exporting single signal: [Errno 2] No such file or directory: 'signal_buy_USD/CAD_20250530_164237.json'
2025-05-30 16:42:37 | SignalGenerator | INFO | [SIGNAL] BUY | USD/CAD | Confidence: 70.00%
2025-05-30 16:42:37 | SignalGenerator | INFO | [REASONING] The USD/CAD pair is showing signs of potential upward momentum. The RSI is at 36.4998, indicating that the pair is approaching oversold conditions, which could lead to a bounce. The MACD is negative, but the histogram is narrowing, suggesting a potential reversal. The recent price action shows a series of higher closes, indicating bullish momentum. The current price is near the lower Bollinger Band, which often acts as a support level. Additionally, the exchange rate is below the EMA of 1.3841, suggesting room for upward movement. Given these technical indicators and the lack of significant economic news that could adversely affect the USD, a cautious buy is recommended.
2025-05-30 16:42:37 | SignalGenerator | INFO | [APPROVE] Signal approved for execution: BUY USD/CAD (Confidence: 70.00%)
2025-05-30 16:43:37 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:43:37 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:43:39 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:43:39 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:43:39 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:43:39 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:43:40 | MarketData | INFO | Quote for USD/CAD: $1.3742
2025-05-30 16:43:40 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:43:41 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:43:41 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:43:43 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:43:43 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:43:44 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:43:44 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:43:45 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:43:45 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:43:47 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:43:47 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:43:48 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:43:48 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:43:48 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:43:56 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 70.00%)
2025-05-30 16:43:56 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 70.00%
2025-05-30 16:43:56 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a mixed outlook. The RSI is at 36.55, indicating the pair is approaching oversold territory, which could suggest a potential for a rebound. However, the MACD is negative, with a MACD histogram of -0.0009, indicating bearish momentum. The price is currently below the EMA of 1.3841, suggesting a bearish trend. The Bollinger Bands show the price near the lower band at 1.3701, which could indicate a potential support level. Given the current P&L of +0.01% and the proximity to the entry price, it is prudent to hold the position and monitor for any signs of reversal or further bearish momentum.

Current Position: LONG_POSITION at 1.37413. Current P&L: +0.01%
2025-05-30 16:43:56 | SignalGenerator | INFO | [INFO] HOLD signal - no action required
2025-05-30 16:44:56 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:44:56 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:44:57 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:44:57 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:44:57 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:44:57 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:44:59 | MarketData | INFO | Quote for USD/CAD: $1.3742
2025-05-30 16:44:59 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:45:00 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:45:00 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:45:01 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:45:01 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:45:02 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:45:03 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:45:04 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:45:04 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:45:05 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:45:05 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:45:07 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:45:07 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:45:07 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:45:14 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 70.00%)
2025-05-30 16:45:14 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 70.00%
2025-05-30 16:45:14 | SignalGenerator | INFO | [REASONING] The current position is slightly profitable with a P&L of +0.01%. The RSI is at 36.56, indicating the pair is nearing oversold territory, which could suggest a potential reversal or stabilization. The MACD is slightly negative, with a histogram showing a minor bearish momentum, but not strong enough to warrant an immediate exit. The current price is below the EMA of 1.3841, suggesting a bearish trend, but the price is close to the lower Bollinger Band at 1.3701, which may act as a support level. Given the current technical setup and the lack of strong bearish signals, it is prudent to hold the position and monitor for any changes in momentum or a clearer trend direction.

Current Position: LONG_POSITION at 1.37413. Current P&L: +0.01%
2025-05-30 16:45:14 | SignalGenerator | INFO | [INFO] HOLD signal - no action required
2025-05-30 16:46:14 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:46:14 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:46:15 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:46:15 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:46:15 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:46:15 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:46:17 | MarketData | INFO | Quote for USD/CAD: $1.3742
2025-05-30 16:46:17 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:46:18 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:46:18 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:46:19 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:46:19 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:46:21 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:46:21 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:46:22 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:46:22 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:46:23 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:46:23 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:46:24 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:46:24 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:46:25 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:46:29 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 70.00%)
2025-05-30 16:46:30 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 70.00%
2025-05-30 16:46:30 | SignalGenerator | INFO | [REASONING] The current position is at breakeven with a slight positive P&L. The RSI is at 36.28, indicating the pair is approaching oversold territory, which suggests potential for a reversal or stabilization. The MACD is slightly negative but close to the signal line, showing weak bearish momentum. The price is near the lower Bollinger Band, indicating potential support. Given these mixed signals and the current breakeven position, it is prudent to hold and monitor for further confirmation of direction.

Current Position: LONG_POSITION at 1.37413. Current P&L: +0.00%
2025-05-30 16:46:30 | SignalGenerator | INFO | [INFO] HOLD signal - no action required
2025-05-30 16:47:30 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:47:30 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:47:31 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:47:31 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:47:31 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:47:31 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:47:33 | MarketData | INFO | Quote for USD/CAD: $1.3738
2025-05-30 16:47:33 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:47:34 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:47:34 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:47:35 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:47:35 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:47:37 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:47:37 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:47:38 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:47:38 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:47:39 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:47:39 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:47:40 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:47:40 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:47:40 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:47:50 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 70.00%)
2025-05-30 16:47:50 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 70.00%
2025-05-30 16:47:50 | SignalGenerator | INFO | [REASONING] The current position is slightly negative, but the technical indicators suggest a potential for stabilization or slight upward movement. The RSI is below 50, indicating the pair is not overbought, and could potentially rebound. The MACD is negative, but the histogram shows a slight reduction in bearish momentum. The price is near the lower Bollinger Band, which often acts as a support level. Given these factors, it is prudent to hold the position and monitor for any signs of reversal or further decline.

Current Position: LONG_POSITION at 1.37413. Current P&L: -0.02%
2025-05-30 16:47:50 | SignalGenerator | INFO | [INFO] HOLD signal - no action required
2025-05-30 16:48:50 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:48:50 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:48:51 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:48:51 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:48:51 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:48:51 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:48:52 | MarketData | INFO | Quote for USD/CAD: $1.3738
2025-05-30 16:48:53 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:48:54 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:48:54 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:48:55 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:48:55 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:48:56 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:48:56 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:48:58 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:48:58 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:48:59 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:48:59 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:49:00 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:49:00 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:49:00 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:49:12 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 70.00%)
2025-05-30 16:49:12 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 70.00%
2025-05-30 16:49:12 | SignalGenerator | INFO | [REASONING] The USD/CAD pair is currently experiencing a slight downtrend, as indicated by the negative MACD and RSI below 50, suggesting bearish momentum. However, the RSI is not yet in oversold territory, which could imply potential for further downside before a reversal. The price is currently trading near the lower Bollinger Band, which often acts as a support level, suggesting limited immediate downside risk. Given the current P&L of -0.02% and the proximity to the entry price, it is prudent to hold the position and monitor for potential reversal signals or further confirmation of a downward trend.

Current Position: LONG_POSITION at 1.37413. Current P&L: -0.02%
2025-05-30 16:49:12 | SignalGenerator | INFO | [INFO] HOLD signal - no action required
2025-05-30 16:50:12 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:50:13 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:50:14 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:50:14 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:50:14 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:50:14 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:50:15 | MarketData | INFO | Quote for USD/CAD: $1.3738
2025-05-30 16:50:15 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:50:16 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:50:17 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:50:18 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:50:18 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:50:19 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:50:19 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:50:20 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:50:20 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:50:22 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:50:22 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:50:23 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:50:23 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:50:23 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:50:28 | AIAnalyzer | INFO | [AI] Analysis complete: EXIT (confidence: 75.00%)
2025-05-30 16:50:28 | SignalGenerator | INFO | [STATE_CHANGE] USD/CAD: LONG_POSITION → NO_POSITION
2025-05-30 16:50:28 | SignalGenerator | ERROR | Error exporting single signal: [Errno 2] No such file or directory: 'signal_exit_USD/CAD_20250530_165028.json'
2025-05-30 16:50:28 | SignalGenerator | INFO | [SIGNAL] EXIT | USD/CAD | Confidence: 75.00%
2025-05-30 16:50:28 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a bearish sentiment. The RSI is at 36.27, indicating that the pair is approaching oversold territory but not yet there, suggesting potential for further downside. The MACD is negative with a bearish histogram, indicating downward momentum. The price is below the EMA of 1.3841, suggesting a bearish trend. Additionally, the price is near the lower Bollinger Band, which could indicate a continuation of the downtrend. Given the current P&L of -0.02%, it is prudent to exit the position to prevent further losses, as the technical indicators do not support a strong rebound at this time.

Current Position: LONG_POSITION at 1.37413. Current P&L: -0.02%
2025-05-30 16:50:28 | SignalGenerator | INFO | [APPROVE] EXIT signal approved for execution: EXIT USD/CAD (Confidence: 75.00%)
2025-05-30 16:51:28 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:51:28 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:51:29 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:51:29 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:51:29 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:51:30 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:51:31 | MarketData | INFO | Quote for USD/CAD: $1.3737
2025-05-30 16:51:31 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:51:32 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:51:32 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:51:33 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:51:33 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:51:35 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:51:35 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:51:36 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:51:36 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:51:37 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:51:37 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:51:39 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:51:39 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:51:39 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:51:53 | AIAnalyzer | INFO | [AI] Analysis complete: SELL (confidence: 70.00%)
2025-05-30 16:51:53 | SignalGenerator | INFO | [STATE_CHANGE] USD/CAD: NO_POSITION → SHORT_POSITION
2025-05-30 16:51:53 | SignalGenerator | ERROR | Error exporting single signal: [Errno 2] No such file or directory: 'signal_sell_USD/CAD_20250530_165153.json'
2025-05-30 16:51:53 | SignalGenerator | INFO | [SIGNAL] SELL | USD/CAD | Confidence: 70.00%
2025-05-30 16:51:53 | SignalGenerator | INFO | [REASONING] The USD/CAD pair is showing signs of bearish momentum based on several technical indicators and recent price action. The RSI is at 36.1992, indicating that the pair is approaching oversold conditions but still has room to move lower. The MACD is negative with a MACD histogram of -0.0009, suggesting bearish momentum. The price is trading below the EMA of 1.3841, which indicates a downtrend. Additionally, the price is near the lower Bollinger Band at 1.3700, which could act as a support level, but the overall trend remains bearish. Given these factors, a short position could be considered with a conservative target and stop loss.
2025-05-30 16:51:54 | SignalGenerator | INFO | [APPROVE] Signal approved for execution: SELL USD/CAD (Confidence: 70.00%)
2025-05-30 16:52:54 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:52:54 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:52:55 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:52:55 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:52:55 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:52:55 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:52:56 | MarketData | INFO | Quote for USD/CAD: $1.3739
2025-05-30 16:52:57 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:52:58 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:52:58 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:52:59 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:52:59 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:53:00 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:53:00 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:53:02 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:53:02 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:53:03 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:53:03 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:53:04 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:53:04 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:53:04 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:53:11 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 70.00%)
2025-05-30 16:53:11 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 70.00%
2025-05-30 16:53:11 | SignalGenerator | INFO | [REASONING] The current short position in USD/CAD is slightly negative, but the technical indicators suggest potential further downside. The RSI is at 36.2889, indicating the pair is approaching oversold territory but not yet there, suggesting there might be more room for downside. The MACD is negative with a histogram also negative, indicating bearish momentum. The price is trading below the EMA of 1.3841, which is a bearish signal. The lower Bollinger Band is at 1.3700, which could act as a near-term support level. Given these factors, it is prudent to hold the short position as the technical indicators suggest a continuation of the bearish trend. However, caution is warranted due to the proximity to the lower Bollinger Band.

Current Position: SHORT_POSITION at 1.37373. Current P&L: -0.01%
2025-05-30 16:53:11 | SignalGenerator | INFO | [INFO] HOLD signal - no action required
2025-05-30 16:54:11 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:54:11 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:54:13 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:54:13 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:54:13 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:54:13 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:54:14 | MarketData | INFO | Quote for USD/CAD: $1.3738
2025-05-30 16:54:14 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:54:15 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:54:15 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:54:17 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:54:17 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:54:18 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:54:18 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:54:19 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:54:19 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:54:20 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:54:21 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:54:22 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:54:22 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:54:22 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:54:33 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 70.00%)
2025-05-30 16:54:33 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 70.00%
2025-05-30 16:54:33 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a bearish sentiment, with the RSI at 36.2664 indicating the pair is approaching oversold conditions, but not yet there. The MACD is negative, with a histogram showing a slight bearish momentum. The price is trading below the EMA of 1.3841, indicating a downtrend. The Bollinger Bands show the price near the lower band, suggesting potential support around 1.3700. Given the current short position, it is prudent to hold as the technical indicators do not yet signal a reversal. The current P&L is slightly negative, but the proximity to the lower Bollinger Band suggests a potential bounce, which could improve the position.

Current Position: SHORT_POSITION at 1.37373. Current P&L: -0.01%
2025-05-30 16:54:33 | SignalGenerator | INFO | [INFO] HOLD signal - no action required
2025-05-30 16:55:33 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:55:33 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:55:34 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:55:34 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:55:34 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:55:35 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:55:36 | MarketData | INFO | Quote for USD/CAD: $1.3738
2025-05-30 16:55:36 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:55:37 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:55:37 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:55:38 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:55:38 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:55:40 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:55:40 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:55:41 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:55:41 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:55:42 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:55:42 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:55:44 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:55:44 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:55:44 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:55:49 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 70.00%)
2025-05-30 16:55:49 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 70.00%
2025-05-30 16:55:49 | SignalGenerator | INFO | [REASONING] The current short position in USD/CAD is slightly negative, but technical indicators suggest potential for further downside. The RSI is at 36.2664, indicating the pair is approaching oversold conditions but not yet at extreme levels. The MACD is negative with a histogram below zero, suggesting bearish momentum. The price is below the EMA of 1.3841, indicating a downtrend. The lower Bollinger Band at 1.3700 provides a potential support level, which is close to the current price, suggesting limited downside risk. Given the current P&L of -0.01%, it is prudent to hold the position and monitor for further bearish confirmation or a reversal signal.

Current Position: SHORT_POSITION at 1.37373. Current P&L: -0.01%
2025-05-30 16:55:49 | SignalGenerator | INFO | [INFO] HOLD signal - no action required
2025-05-30 16:56:49 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:56:49 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:56:50 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:56:50 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:56:50 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:56:50 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:56:51 | MarketData | INFO | Quote for USD/CAD: $1.3737
2025-05-30 16:56:52 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:56:53 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:56:53 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:56:54 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:56:54 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:56:55 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:56:55 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:56:57 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:56:57 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:56:58 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:56:58 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:56:59 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:56:59 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:56:59 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:57:05 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 70.00%)
2025-05-30 16:57:05 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 70.00%
2025-05-30 16:57:05 | SignalGenerator | INFO | [REASONING] The current short position is at breakeven, and technical indicators suggest a potential continuation of bearish momentum. The RSI is at 36.18, indicating the pair is approaching oversold territory but not yet there, suggesting room for further downside. The MACD is negative, with a histogram below zero, reinforcing bearish sentiment. The EMA is significantly above the current price, indicating a downtrend. The price is near the lower Bollinger Band, which could act as support, but the lack of a significant bounce suggests the trend may continue. Given the current technical setup and the absence of a strong reversal signal, it is prudent to hold the short position.

Current Position: SHORT_POSITION at 1.37373. Current P&L: +0.00%
2025-05-30 16:57:05 | SignalGenerator | INFO | [INFO] HOLD signal - no action required
2025-05-30 16:58:05 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:58:05 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:58:07 | MarketData | INFO | Quote for AAPL: $200.6700
2025-05-30 16:58:07 | EnhancedMarketData | INFO | Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:58:07 | MarketData | INFO | Fetching comprehensive data for USD/CAD
2025-05-30 16:58:07 | MarketData | INFO | Making API request to quote (attempt 1/3)
2025-05-30 16:58:08 | MarketData | INFO | Quote for USD/CAD: $1.3734
2025-05-30 16:58:08 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:58:09 | MarketData | INFO | Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:58:09 | MarketData | INFO | Making API request to time_series (attempt 1/3)
2025-05-30 16:58:11 | MarketData | INFO | Retrieved 100 daily data points for USD/CAD
2025-05-30 16:58:11 | MarketData | INFO | Making API request to rsi (attempt 1/3)
2025-05-30 16:58:12 | MarketData | INFO | Retrieved RSI data for USD/CAD
2025-05-30 16:58:12 | MarketData | INFO | Making API request to macd (attempt 1/3)
2025-05-30 16:58:13 | MarketData | INFO | Retrieved MACD data for USD/CAD
2025-05-30 16:58:13 | MarketData | INFO | Making API request to ema (attempt 1/3)
2025-05-30 16:58:15 | MarketData | INFO | Retrieved EMA data for USD/CAD
2025-05-30 16:58:15 | MarketData | INFO | Making API request to bbands (attempt 1/3)
2025-05-30 16:58:16 | MarketData | INFO | Retrieved BBANDS data for USD/CAD
2025-05-30 16:58:16 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:58:16 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:58:22 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 70.00%)
2025-05-30 16:58:22 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/CAD | Confidence: 70.00%
2025-05-30 16:58:22 | SignalGenerator | INFO | [REASONING] The USD/CAD pair is currently in a short position with a slight profit. The RSI is at 35.9551, indicating the pair is approaching oversold territory, but not yet there, suggesting potential for further downside. The MACD is negative, with the MACD line below the signal line, indicating bearish momentum. The current price is below the EMA of 1.3841, reinforcing the bearish trend. The price is also close to the lower Bollinger Band at 1.3699, suggesting limited downside before potential support. Given these factors, maintaining the short position is advisable to capture further potential downside, but with caution due to proximity to support levels.

Current Position: SHORT_POSITION at 1.37373. Current P&L: +0.02%
2025-05-30 16:58:22 | SignalGenerator | INFO | [INFO] HOLD signal - no action required
2025-05-30 16:59:17 | TradingBot | INFO | [SHUTDOWN] Shutdown signal received, stopping bot...
2025-05-30 16:59:17 | SignalGenerator | INFO | [EXPORT] Exported 5 signals to trading_signals_20250530_165917.json
2025-05-30 16:59:17 | EnhancedMarketData | INFO | Enhanced market data provider cleaned up
2025-05-30 16:59:17 | TradingBot | INFO | Trading bot stopped
2025-05-30 16:59:17 | SignalGenerator | INFO | [EXPORT] Exported 5 signals to trading_signals_20250530_165917.json
2025-05-30 16:59:17 | EnhancedMarketData | INFO | Enhanced market data provider cleaned up
2025-05-30 16:59:17 | TradingBot | INFO | Trading bot stopped
2025-05-30 23:47:49 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:47:50 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:47:56 | DerivAPI | ERROR | [ERROR] in sending sync request: 
Traceback (most recent call last):
  File "E:\Desktop\BINARY\deriv_api.py", line 230, in send_request_sync
    return future.result(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\concurrent\futures\_base.py", line 447, in result
    raise TimeoutError()
concurrent.futures._base.TimeoutError
2025-05-30 23:47:56 | DerivAPI | ERROR | Request 2 timed out
2025-05-30 23:48:00 | DerivAPI | ERROR | Request 1 timed out
2025-05-30 23:48:00 | DerivAPI | ERROR | Authentication failed: No response
2025-05-30 23:48:00 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:48:00 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:48:01 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:48:01 | DerivMarketData | INFO | Deriv market data provider started
2025-05-30 23:48:01 | DerivAPI | ERROR | WebSocket not connected
2025-05-30 23:48:01 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-30 23:48:01 | DerivAPI | ERROR | WebSocket not connected
2025-05-30 23:48:01 | DerivMarketData | ERROR | Failed to get tick history for R_10: No response
2025-05-30 23:48:02 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:48:06 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-30 23:48:47 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:48:48 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:48:54 | DerivAPI | ERROR | Request 2 timed out
2025-05-30 23:48:54 | DerivAPI | ERROR | [ERROR] in sending sync request: 
Traceback (most recent call last):
  File "E:\Desktop\BINARY\deriv_api.py", line 230, in send_request_sync
    return future.result(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\concurrent\futures\_base.py", line 447, in result
    raise TimeoutError()
concurrent.futures._base.TimeoutError
2025-05-30 23:48:58 | DerivAPI | ERROR | Request 1 timed out
2025-05-30 23:48:58 | DerivAPI | ERROR | Authentication failed: No response
2025-05-30 23:48:58 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:48:58 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:48:59 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:48:59 | DerivMarketData | INFO | Deriv market data provider started
2025-05-30 23:48:59 | DerivAPI | ERROR | WebSocket not connected
2025-05-30 23:48:59 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-30 23:48:59 | DerivAPI | ERROR | WebSocket not connected
2025-05-30 23:48:59 | DerivMarketData | ERROR | Failed to get tick history for R_10: No response
2025-05-30 23:49:00 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:49:05 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-30 23:51:12 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:51:12 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:51:19 | DerivAPI | ERROR | [ERROR] in sending sync request: 
Traceback (most recent call last):
  File "E:\Desktop\BINARY\deriv_api.py", line 230, in send_request_sync
    return future.result(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\concurrent\futures\_base.py", line 447, in result
    raise TimeoutError()
concurrent.futures._base.TimeoutError
2025-05-30 23:51:19 | DerivAPI | ERROR | Request 2 timed out
2025-05-30 23:51:22 | DerivAPI | ERROR | Request 1 timed out
2025-05-30 23:51:22 | DerivAPI | ERROR | Authentication failed: No response
2025-05-30 23:51:22 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:51:23 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:53:52 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:53:53 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:54:00 | DerivAPI | ERROR | Request 2 timed out
2025-05-30 23:54:00 | DerivAPI | ERROR | [ERROR] in sending sync request: 
Traceback (most recent call last):
  File "E:\Desktop\BINARY\deriv_api.py", line 230, in send_request_sync
    return future.result(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\concurrent\futures\_base.py", line 447, in result
    raise TimeoutError()
concurrent.futures._base.TimeoutError
2025-05-30 23:54:03 | DerivAPI | ERROR | Request 1 timed out
2025-05-30 23:54:03 | DerivAPI | ERROR | Authentication failed: No response
2025-05-30 23:54:03 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:54:04 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:54:05 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-30 23:54:05 | DerivMarketData | INFO | Deriv market data provider started
2025-05-30 23:54:05 | DerivAPI | ERROR | WebSocket not connected
2025-05-30 23:54:05 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-30 23:54:05 | DerivAPI | ERROR | WebSocket not connected
2025-05-30 23:54:05 | DerivMarketData | ERROR | Failed to get tick history for R_10: No response
2025-05-30 23:54:05 | DerivAPI | INFO | Connected to Deriv API
2025-05-30 23:54:10 | DerivMarketData | INFO | Deriv market data provider stopped
