"""
Test Deriv API Connection
Simple script to test the Deriv API connection and basic functionality
"""
import asyncio
import time
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

from deriv_config import DerivConfig
from deriv_api import DerivAPIClient
from deriv_market_data import DerivMarketDataProvider

def test_configuration():
    """Test configuration setup"""
    console = Console()

    console.print("[bold blue]Testing Configuration...[/bold blue]")

    # Check required configuration
    issues = []
    warnings = []

    if not DerivConfig.DERIV_API_TOKEN:
        issues.append("DERIV_API_TOKEN not set")
    elif len(DerivConfig.DERIV_API_TOKEN) < 10:
        warnings.append(f"DERIV_API_TOKEN seems too short ({len(DerivConfig.DERIV_API_TOKEN)} chars). Valid tokens are usually 15+ characters.")

    if not DerivConfig.DERIV_APP_ID:
        issues.append("DERIV_APP_ID not set")

    if issues:
        console.print("[red]Configuration Issues:[/red]")
        for issue in issues:
            console.print(f"  ❌ {issue}")
        console.print("\n[yellow]Please check your .env file[/yellow]")
        console.print("\n[blue]To get a valid Deriv API token:[/blue]")
        console.print("  1. Go to https://app.deriv.com/account/api-token")
        console.print("  2. Create a new token with 'Read' and 'Trade' permissions")
        console.print("  3. Copy the token to your .env file")
        return False

    if warnings:
        console.print("[yellow]Configuration Warnings:[/yellow]")
        for warning in warnings:
            console.print(f"  ⚠ {warning}")
        console.print("\n[blue]If you're having connection issues:[/blue]")
        console.print("  1. Verify your token at https://app.deriv.com/account/api-token")
        console.print("  2. Make sure the token has 'Read' and 'Trade' permissions")
        console.print("  3. Check that your Deriv account is active")

    console.print("[green]✓ Configuration looks good[/green]")
    console.print(f"  App ID: {DerivConfig.DERIV_APP_ID}")
    console.print(f"  Token: {'*' * 20}...{DerivConfig.DERIV_API_TOKEN[-4:] if len(DerivConfig.DERIV_API_TOKEN) > 4 else 'SET'}")
    return True

def test_api_connection():
    """Test basic API connection"""
    console = Console()
    
    console.print("\n[bold blue]Testing API Connection...[/bold blue]")
    
    try:
        # Create API client
        client = DerivAPIClient()
        
        # Start connection
        client.start()
        
        # Wait for connection and authentication
        time.sleep(3)  # Give more time for authentication
        
        if client.is_connected():
            console.print("[green]✓ Connected to Deriv API[/green]")

            auth_success = False
            api_comm_success = False

            # Test authentication if token is provided
            if DerivConfig.DERIV_API_TOKEN and client.is_authenticated_user():
                console.print("[green]✓ Authentication successful[/green]")
                auth_success = True
            elif DerivConfig.DERIV_API_TOKEN:
                console.print("[red]✗ Authentication failed[/red]")
                auth_success = False
            else:
                console.print("[yellow]⚠ Connected without authentication (no token)[/yellow]")
                auth_success = True  # No token expected, so this is OK

            # Test basic API call
            try:
                console.print("[blue]Testing API communication with ping...[/blue]")
                response = client.send_request_sync({"ping": 1}, timeout=10)
                if response and response.data.get('ping') == 'pong':
                    console.print("[green]✓ API communication working[/green]")
                    api_comm_success = True
                elif response:
                    console.print(f"[red]✗ API communication failed - Unexpected response: {response.data}[/red]")
                    api_comm_success = False
                else:
                    console.print("[red]✗ API communication failed - No response received[/red]")
                    api_comm_success = False
            except Exception as e:
                console.print(f"[red]✗ API test failed: {e}[/red]")
                api_comm_success = False

            client.stop()
            return auth_success and api_comm_success
        else:
            console.print("[red]✗ Failed to connect to Deriv API[/red]")
            client.stop()
            return False
            
    except Exception as e:
        console.print(f"[red]✗ Connection test failed: {e}[/red]")
        return False

def test_market_data():
    """Test market data functionality"""
    console = Console()
    
    console.print("\n[bold blue]Testing Market Data...[/bold blue]")
    
    try:
        # Create market data provider
        market_data = DerivMarketDataProvider()
        
        # Start market data
        if not market_data.start():
            console.print("[red]✗ Failed to start market data provider[/red]")
            return False
        
        console.print("[green]✓ Market data provider started[/green]")
        
        # Wait for market data connection
        console.print("[blue]Waiting for market data connection...[/blue]")
        for i in range(10):
            if market_data.is_connected():
                break
            console.print(f"  Waiting... {i+1}/10")
            time.sleep(1)

        if not market_data.is_connected():
            console.print("[red]✗ Market data provider not connected[/red]")
            market_data.stop()
            return False

        console.print("[green]✓ Market data provider connected[/green]")

        # Test getting active symbols
        symbols_success = False
        tick_history_success = False

        symbols = market_data.get_active_symbols()
        if symbols:
            console.print(f"[green]✓ Retrieved {len(symbols)} active symbols[/green]")
            symbols_success = True

            # Display some symbols
            table = Table(title="Sample Active Symbols")
            table.add_column("Symbol", style="cyan")
            table.add_column("Display Name", style="white")
            table.add_column("Market", style="yellow")

            for symbol in symbols[:5]:  # Show first 5
                table.add_row(
                    symbol['symbol'],
                    symbol['display_name'],
                    symbol['market']
                )

            console.print(table)
        else:
            console.print("[red]✗ Failed to retrieve active symbols[/red]")
            symbols_success = False

        # Test tick history for a common symbol
        test_symbol = "R_10"  # Volatility 10 Index
        console.print(f"\n[blue]Testing tick history for {test_symbol}...[/blue]")

        tick_history = market_data.get_tick_history(test_symbol, count=10)
        if tick_history is not None and not tick_history.empty:
            console.print(f"[green]✓ Retrieved {len(tick_history)} ticks for {test_symbol}[/green]")
            console.print(f"  Latest price: {tick_history.iloc[-1]['price']:.5f}")
            tick_history_success = True
        else:
            console.print(f"[red]✗ Failed to get tick history for {test_symbol}[/red]")
            tick_history_success = False

        market_data.stop()
        return symbols_success and tick_history_success
        
    except Exception as e:
        console.print(f"[red]✗ Market data test failed: {e}[/red]")
        return False

def test_binary_options_symbols():
    """Test binary options symbols configuration"""
    console = Console()
    
    console.print("\n[bold blue]Testing Binary Options Symbols...[/bold blue]")
    
    symbols = DerivConfig.BINARY_OPTIONS_SYMBOLS
    console.print(f"Configured symbols: {', '.join(symbols)}")
    
    # Test symbol classification
    table = Table(title="Symbol Classification")
    table.add_column("Symbol", style="cyan")
    table.add_column("Type", style="white")
    table.add_column("Info", style="yellow")
    
    for symbol in symbols:
        if DerivConfig.is_synthetic_index(symbol):
            symbol_type = "Synthetic Index"
            info = DerivConfig.SYNTHETIC_INDICES[symbol]['name']
        elif DerivConfig.is_forex_symbol(symbol):
            symbol_type = "Forex"
            info = "Currency Pair"
        else:
            symbol_type = "Unknown"
            info = "Not classified"
        
        table.add_row(symbol, symbol_type, info)
    
    console.print(table)
    return True

def main():
    """Run all tests"""
    console = Console()
    
    console.print(Panel.fit(
        "[bold white]Deriv API Connection Test[/bold white]\n"
        "This script tests the basic functionality of the Deriv API integration",
        border_style="blue"
    ))
    
    # Run tests
    tests = [
        ("Configuration", test_configuration),
        ("API Connection", test_api_connection),
        ("Market Data", test_market_data),
        ("Binary Options Symbols", test_binary_options_symbols)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            console.print(f"[red]✗ {test_name} test crashed: {e}[/red]")
            results.append((test_name, False))
    
    # Summary
    console.print("\n[bold blue]Test Summary:[/bold blue]")
    passed = 0
    for test_name, result in results:
        status = "[green]✓ PASS[/green]" if result else "[red]✗ FAIL[/red]"
        console.print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    console.print(f"\n[bold]Results: {passed}/{len(results)} tests passed[/bold]")
    
    if passed == len(results):
        console.print("\n[green]🎉 All tests passed! Your setup is ready for binary options trading.[/green]")
        console.print("\n[blue]Next steps:[/blue]")
        console.print("  1. Run: python binary_options_bot.py --symbol R_10")
        console.print("  2. For continuous trading: python binary_options_bot.py --continuous")
    else:
        console.print("\n[red]❌ Some tests failed. Please check your configuration.[/red]")
        console.print("\n[yellow]Common issues:[/yellow]")
        console.print("  1. Check your .env file has correct DERIV_API_TOKEN")
        console.print("  2. Ensure your Deriv account is active")
        console.print("  3. Verify internet connection")

if __name__ == "__main__":
    main()
